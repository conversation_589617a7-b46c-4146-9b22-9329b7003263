# ProofPay: ERP Integration Middleware For Private Stablecoin Payments On Proofbase

## Overview

ProofPay is an ERP transaction management platform built to streamline accounts payable and receivable operations with blockchain integration for private stablecoin payments. The application features a powerful **Workflow Engine** that can import approval rules from SAP, Oracle, Dynamics 365, and NetSuite, replacing static approval predicates with dynamic, rule-based workflows. It supports standard ERP payment, invoice, and reconciliation file formats to provide simple, real-time payment processing on blockchain, without the complexity.

## Key Features

### Accounts Payable
- **Payment Management**: Import, approve, and process outgoing payments
- **Approval Workflow**: Structured approval flow with revocation capabilities
- **BLS Cryptographic Signatures**: Secure payment authorization with cryptographic proof
- **Blockchain Integration**: Simulated blockchain payment sending
- **Manual Receipt Import**: Import payment receipts for reconciliation
- **Reconciliation**: Generate standardized reconciliation documents in various formats (MT940, BAI2, ISO20022)

### Accounts Receivable
- **Invoice Management**: Create and track invoices with automatic status updates
- **Payment Receiving**: Monitor and process incoming payments
- **Manual Payment Import**: Import received payments via JSON data
- **Payment-Invoice Linking**: Connect received payments to open invoices
- **Reconciliation Generation**: Create standardized reconciliation documents

### Workflow Engine (NEW)
- **Multi-ERP Import**: Import approval workflows from SAP, Oracle, Dynamics 365, and NetSuite
- **Dynamic Rule Engine**: Replace static approval predicates with configurable, rule-based workflows
- **Real-time Evaluation**: Sub-5ms document evaluation with caching for optimal performance
- **Field Mapping Wizard**: Interactive wizard to map vendor-specific fields to canonical schema
- **Version Control**: Complete audit trail with diff viewing and rollback capabilities
- **Simulation Sandbox**: Test workflow rules with sample documents before deployment
- **API & File Connectors**: Automated imports via API polling or file upload (JSON, XML, XLSX, CSV, ZIP)

### Admin Interface
- **Organization Management**: Configure company details, ERP integration settings
- **Member Management**: Role-based access control with Approver/AP/AR permissions
- **Workflow Engine**: Import, configure, and test approval workflows from ERP systems
- **Approval Matrix**: Visual overview of approval routing with rule sources and step sequences

### Shared Capabilities
- **File Import/Export**: Support for multiple financial document formats
- **Historical Tracking**: Permanent record of all transactions
- **Status Filtering**: Filter by payment/invoice status
- **Search Functionality**: Find payments and invoices across the system

## Technical Architecture

### Frontend
- **Framework**: React with TypeScript
- **State Management**: TanStack Query (React Query) for server state
- **Styling**: Tailwind CSS with Shadcn/UI components
- **Routing**: Wouter for lightweight routing
- **Form Handling**: React Hook Form with Zod validation

### Backend
- **Server**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Express-session with Passport.js
- **API Layer**: RESTful API for all financial operations
- **Workflow Engine**: High-performance rule evaluation engine with caching
- **ERP Parsers**: Multi-format parsers for SAP, Oracle, Dynamics 365, NetSuite

## Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL database

### Installation

1. Clone the repository
```
git clone https://github.com/yourusername/proofpay.git
cd proofpay
```

2. Install dependencies
```
npm install
```

3. Configure environment variables
```
# Create a .env file with the following variables
DATABASE_URL=postgresql://username:password@localhost:5432/proofpay

# BLS signature configuration
BLS_PRIVATE_KEY=your_private_key_here  # For testing only, use proper key management in production
BLS_PUBLIC_KEY=your_public_key_here    # For signature verification

# Network simulation configuration
SIMULATOR_ENABLED=false                # Set to true to enable simulated network delays
```

4. Run database migrations
```
npm run db:push
```

5. Start the development server
```
npm run dev
```

## Usage Guides

### Admin Setup & Workflow Configuration

1. **Access Admin Interface**: Navigate to `/admin` to configure organization and workflows
2. **Organization Setup**: Configure company details, ERP integration settings, and fiscal currency
3. **Member Management**: Add team members with appropriate roles (Approver/AP/AR)
4. **Workflow Engine Setup**:
   - Go to "Workflow Engine" tab
   - Import approval rules from your ERP system (SAP, Oracle, Dynamics 365, NetSuite)
   - Use the mapping wizard to map vendor fields to canonical schema
   - Test workflows in the simulation sandbox
5. **Approval Matrix**: Review the approval routing and rule sources

### Workflow Engine Process Flow

1. **Import ERP Workflows**:
   - Select your ERP vendor (SAP, Oracle, Dynamics 365, NetSuite)
   - Upload workflow files (JSON, XML, XLSX, CSV, ZIP formats)
   - Follow the 3-step mapping wizard to map vendor fields
2. **Configure API Connectors** (Optional):
   - Set up automated imports with API credentials
   - Configure polling intervals for real-time sync
3. **Test & Simulate**:
   - Use the simulation sandbox to test approval routing
   - Try different document amounts and scenarios
   - Verify approval paths match expectations
4. **Version Management**:
   - View version history with diff comparison
   - Rollback to previous versions if needed
   - Track all changes for audit compliance

### Accounts Payable Process Flow

1. **Import Payment Files**: Use the "Import" button to upload payment files (PEXR2002, MT103, ISO20022 formats)
2. **Workflow Evaluation**: Documents automatically flow through configured approval workflows
3. **Approve Payments**: Review and approve payments based on workflow rules (generates BLS signatures)
4. **Send Payments**: Send approved payments to the blockchain network (downloads transaction file)
5. **Import Receipt**: Import receipt for a sent payment via JSON format
6. **Generate Reconciliation**: Create reconciliation documents for sent payments

### Accounts Receivable Process Flow

1. **Create/Import Invoices**: Use the "Import" button to upload invoice files (EDI X12, ISO20022 formats)
2. **Workflow Processing**: Invoices are evaluated against approval workflows for routing
3. **Monitor Incoming Payments**: View received payments in the "Received Payments" column
4. **Import Manual Payment**: Import received payment via JSON data
5. **Link Payments**: Connect received payments to open invoices
6. **Generate Reconciliation**: Create reconciliation documents for paid invoices

### Payment Import/Export JSON Format

For manual import and receipt functions, use the following JSON format:

**Import Payment (AR):**
```json
{
  "from": "Sender Company Name",
  "amount": 1000.00,
  "reference": "REF-12345"
}
```

**Import Receipt (AP):**
```json
{
  "account": "Recipient Account ID",
  "amount": 1000.00,
  "reference": "REF-12345"
}
```

## File Format Support

### Workflow Import Formats
- **JSON**: SAP release strategies, Oracle BPM definitions
- **XML**: Dynamics 365 workflows, NetSuite SuiteFlow bundles
- **XLSX/CSV**: Oracle BPM tabular exports, custom workflow definitions
- **ZIP**: Multi-file workflow packages with mappings

### Payment Formats
- PEXR2002 (Proprietary Exchange Format)
- MT103 (SWIFT Message Type)
- ISO20022 (International Standard)

### Invoice Formats
- EDI X12 (Electronic Data Interchange)
- ISO20022 (International Standard)

### Reconciliation Formats
- MT940 (SWIFT Customer Statement Message)
- BAI2 (Bank Administration Institute)
- ISO20022 (International Standard)

### ERP-Specific Field Mappings

#### SAP Mappings
- `CEKKO-WRBTR` → `amount` (Invoice amount)
- `CEKKO-WAERS` → `currency` (Currency)
- `CEKKO-LIFNR` → `supplier.id` (Vendor number)
- `CEKKO-BUKRS` → `organization.costCenter` (Company code)

#### Oracle Mappings
- `invoice.amount` → `amount`
- `invoice.currency` → `currency`
- `supplier.id` → `supplier.id`
- `cost.center` → `organization.costCenter`

#### Dynamics 365 Mappings
- `Amount` → `amount`
- `CurrencyCode` → `currency`
- `VendorAccount` → `supplier.id`

#### NetSuite Mappings
- `amount` → `amount`
- `currency` → `currency`
- `vendor` → `supplier.id`
- `subsidiary` → `organization.costCenter`

## Project Structure

```
proofpay/
├── client/                  # Frontend React application
│   ├── src/
│   │   ├── assets/          # Static assets and images
│   │   ├── components/      # Reusable UI components
│   │   │   ├── admin/       # Admin interface components
│   │   │   │   ├── WorkflowEngine.tsx           # Main workflow engine interface
│   │   │   │   ├── WorkflowUploadZone.tsx       # File upload with drag-and-drop
│   │   │   │   ├── WorkflowDefinitionList.tsx   # Workflow management table
│   │   │   │   ├── WorkflowSimulationSandbox.tsx # Testing sandbox
│   │   │   │   ├── MappingWizardModal.tsx       # Field mapping wizard
│   │   │   │   └── WorkflowVersionHistory.tsx   # Version control interface
│   │   │   └── ui/          # Shadcn/UI components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── lib/             # Utility functions and helpers
│   │   ├── pages/           # Page components and routes
│   │   ├── services/        # API services and business logic
│   │   │   ├── workflowEngine.ts    # Core workflow evaluation engine
│   │   │   ├── erpParsers.ts        # Multi-ERP format parsers
│   │   │   └── adminService.ts      # Admin API service
│   │   ├── types/           # TypeScript type definitions
│   │   │   ├── workflow.ts          # Workflow engine types
│   │   │   └── admin.ts             # Admin interface types
│   │   └── App.tsx          # Main application component
├── server/                  # Backend Express application
│   ├── utils/               # Server utility functions
│   ├── index.ts             # Server entry point
│   ├── routes.ts            # API routes (includes workflow endpoints)
│   ├── storage.ts           # Database access layer
│   ├── seedWorkflows.ts     # Demo workflow seeding script
│   └── vite.ts              # Vite development server
├── shared/                  # Shared code between client and server
│   └── schema.ts            # Database schema (includes workflow tables)
├── demo-data/               # Demo workflow files for testing
│   ├── sap_release_strategy.json    # SAP S/4HANA workflows
│   ├── oracle_bpm.xlsx              # Oracle BPM Suite workflows
│   ├── d365_workflow.xml            # Dynamics 365 workflows
│   ├── netsuite_bundle.xml          # NetSuite SuiteFlow workflows
│   └── README.md                    # Demo data documentation
└── files/                   # Uploaded and generated files
```

## API Endpoints

### Workflow Engine APIs
```
POST /api/admin/workflow/import                    # Import workflow files
POST /api/admin/workflow/import/:vendor/pull       # Pull workflows via API
GET  /api/admin/workflow/definitions               # List all workflows
PUT  /api/admin/workflow/definitions/:id           # Update workflow
POST /api/engine/evaluate                          # Evaluate document against workflow
```

### Admin APIs
```
GET  /api/admin/organization                       # Get organization details
PUT  /api/admin/organization                       # Update organization
GET  /api/admin/members                           # Get team members
POST /api/admin/members                           # Add team member
PUT  /api/admin/approval-predicate                # Update approval settings
```

### Core APIs
```
GET  /api/payments                                # Get payments list
POST /api/payments/import                         # Import payment files
POST /api/payments/approve                        # Approve payments
GET  /api/invoices                               # Get invoices list
POST /api/invoices/import                        # Import invoice files
```

## Performance Metrics

### Workflow Engine Performance
- **Evaluation Time**: <5ms per document (p95)
- **Import Time**: <5 seconds per file
- **Cache Hit Rate**: >95% for compiled predicates
- **Memory Usage**: <50MB for 1000+ workflows

### Database Performance
- **Query Response**: <100ms for complex approval matrix queries
- **Concurrent Users**: Supports 100+ simultaneous users
- **Data Integrity**: ACID compliance with PostgreSQL

## Demo Testing Scenarios

Use the demo data package (`demo-data/`) to test workflow functionality:

1. **Low Value Invoice** ($2,500) → Single manager approval (Maria Hughes)
2. **High Value Invoice** ($75,000) → Multi-tier approval (David Nguyen → Alex Choi)
3. **Foreign Currency** (€150,000) → Executive approval (Alex Choi)
4. **Auto-Approval** (<$100 trusted vendors) → No human intervention

### Testing Steps
1. Navigate to `/admin` → "Workflow Engine" tab
2. Upload demo files from `demo-data/` folder
3. Follow mapping wizard to map vendor fields
4. Use "Simulation Sandbox" to test scenarios
5. Verify approval routing matches expectations

## Code Documentation

The codebase is extensively documented with JSDoc comments explaining:

1. Component purposes and behaviors
2. Data flow and state management
3. Complex business logic and financial operations
4. API interactions and optimistic updates
5. Workflow evaluation algorithms and performance optimizations
6. ERP parser implementations and field mapping logic

Key areas of documentation include:

- **Data hooks** (`usePayments`, `useInvoices`, `useReceivedPayments`)
- **UI components** (`KanbanBoard`, `DetailPanel`, card components)
- **Main workflow pages** (`AccountsPayable`, `AccountsReceivable`)
- **Admin interface** (`WorkflowEngine`, `MappingWizardModal`, `WorkflowSimulationSandbox`)
- **Workflow engine** (`workflowEngine.ts`, `erpParsers.ts`)
- **Schema definitions** and database models (including workflow tables)
- **Type definitions** (`workflow.ts`, `admin.ts`) with comprehensive interfaces

## Development Guidelines

- **Status Terminology**: "Reconciled" is used throughout the app (previously "Remitted")
- **Column Consistency**: Items remain in columns even after status changes to maintain context
- **Column Sorting**: Items are sorted by creation/action date, newest first
- **Animations**: Smooth transitions between states without refreshing entire columns
- **Error Handling**: Clear error messages with specific validation requirements

## Testing

The application includes a comprehensive test suite built with:

- **Vitest**: Fast Vite-based test runner
- **React Testing Library**: DOM testing utilities
- **MSW (Mock Service Worker)**: API mocking for tests
- **User Event**: User interaction simulation

### Workflow Engine Testing

Dedicated test suites for the workflow engine:

```bash
# Run workflow engine tests
npm test -- workflowEngine.test.ts

# Run ERP parser tests
npm test -- erpParsers.test.ts

# Run admin interface tests
npm test -- admin.test.ts
```

### Running Tests

Tests can be run using the provided script:

```bash
# Run all tests
./run-tests.sh

# Run tests in watch mode
./run-tests.sh -w

# Run with UI
./run-tests.sh -u

# Run with coverage
./run-tests.sh -c

# Filter tests
./run-tests.sh -f PaymentCard
```

Alternatively, run tests directly with Vitest:

```bash
# Run all tests
npx vitest run

# Run tests in watch mode
npx vitest
```

For more comprehensive testing documentation, see [TESTING.md](TESTING.md).

### Test Coverage

The test suite covers key application areas:

- **Components**: UI rendering and interactions (including workflow engine UI)
- **Hooks**: Data fetching and state management
- **Utility Functions**: Helper functionality
- **API Integration**: Server request/response handling
- **Workflow Engine**: Rule evaluation, predicate logic, performance testing
- **ERP Parsers**: Multi-vendor format parsing and field mapping
- **Admin Interface**: Organization management, member roles, workflow configuration
